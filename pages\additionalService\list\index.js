import additionalServiceApi from '../../../api/modules/additionalService.js';
import payApi from '../../../api/modules/pay.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,

    // 追加服务列表
    additionalServices: [],
    loading: false,

    // 筛选条件
    currentTab: 'all',
    tabs: [
      { key: 'all', label: '全部' },
      { key: 'pending_confirm', label: '待确认' },
      { key: 'confirmed', label: '已确认' },
      { key: 'paid', label: '已付款' },
      { key: 'rejected', label: '已拒绝' },
    ],

    // 状态映射
    statusMap: {
      pending_confirm: { text: '待确认', color: '#ff9500', desc: '等待员工确认' },
      confirmed: { text: '已确认', color: '#007aff', desc: '请尽快完成支付' },
      paid: { text: '已付款', color: '#34c759', desc: '服务进行中' },
      rejected: { text: '已拒绝', color: '#ff3b30', desc: '申请被拒绝' },
    },

    // 支付相关
    paying: false,
    currentPayService: null, // 当前要支付的服务

    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: [],
  },

  onLoad(options) {
    const { orderDetailId } = options;
    const userInfo = Session.getUser();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId),
    });

    this.loadAdditionalServices();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAdditionalServices();
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const { key } = e.currentTarget.dataset;
    this.setData({ currentTab: key });
    this.loadAdditionalServices();
  },

  /**
   * 加载追加服务列表
   */
  async loadAdditionalServices() {
    try {
      this.setData({ loading: true });

      const { orderDetailId, currentTab } = this.data;
      const params = currentTab === 'all' ? {} : { status: currentTab };

      const services = await additionalServiceApi.list(orderDetailId, params);

      // 格式化数据
      const formattedServices = (services || []).map((item, index) => {
        return {
          ...item,
          createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : '',
          confirmTime: item.confirmTime ? utils.formatNormalDate(item.confirmTime) : '',
          statusInfo: this.data.statusMap[item.status] || { text: item.status, color: '#999', desc: '' },
        };
      });

      this.setData({
        additionalServices: formattedServices,
      });
    } catch (error) {
      console.error('加载追加服务列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const { id } = e.currentTarget.dataset;
    const { orderDetailId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${id}`,
    });
  },

  /**
   * 去支付 - 直接在当前页面处理支付
   */
  async goPay(e) {
    const { id } = e.currentTarget.dataset;
    const { additionalServices } = this.data;

    // 找到要支付的服务
    const service = additionalServices.find(item => item.id === parseInt(id));
    if (!service) {
      wx.showToast({
        title: '服务信息不存在',
        icon: 'none',
      });
      return;
    }

    // 检查状态
    if (service.status !== 'confirmed') {
      wx.showToast({
        title: '当前状态不支持支付',
        icon: 'none',
      });
      return;
    }

    // 设置当前支付服务并显示确认模态框
    this.setData({
      currentPayService: service,
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${service.totalFee} 吗？`,
      modalButtons: [
        {
          text: '取消',
          type: 'cancel',
          event: 'handleModalCancel',
        },
        {
          text: '确认支付',
          type: 'primary',
          event: 'handlePayConfirm',
        },
      ],
    });
  },

  /**
   * 申请新的追加服务
   */
  applyNewService() {
    const { orderDetailId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/apply/index?orderDetailId=${orderDetailId}`,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadAdditionalServices().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    this.setData({ showModal: false });
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  },
});
