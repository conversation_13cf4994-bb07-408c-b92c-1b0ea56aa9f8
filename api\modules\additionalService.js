import request, { analysisRes } from '../request';
import config from '../config';

const { additionalService } = config.apiUrls;

export default {
  /**
   * 创建追加服务申请
   * @param {number} orderDetailId 订单详情ID
   * @param {object} data 申请数据
   * @param {number} data.customerId 客户ID
   * @param {array} data.services 追加服务列表
   * @param {array} data.discountInfos 优惠信息列表
   * @param {string} data.remark 备注
   * @returns {Promise<any>} 返回创建结果
   */
  async create(orderDetailId, data) {
    const res = await request.post(
      additionalService.create.replace('{orderDetailId}', orderDetailId),
      data
    );
    return analysisRes(res);
  },

  /**
   * 查询追加服务列表
   * @param {number} orderDetailId 订单详情ID
   * @param {object} params 查询参数
   * @param {string} params.status 状态筛选
   * @returns {Promise<any>} 返回追加服务列表
   */
  async list(orderDetailId, params = {}) {
    const res = await request.get(
      additionalService.list.replace('{orderDetailId}', orderDetailId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 查询追加服务详情
   * @param {number} orderDetailId 订单详情ID
   * @param {number} id 追加服务订单ID
   * @returns {Promise<any>} 返回追加服务详情
   */
  async detail(orderDetailId, id) {
    const res = await request.get(
      additionalService.detail
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', id)
    );
    return analysisRes(res);
  },

  /**
   * 支付追加服务订单
   * @param {number} orderDetailId 订单详情ID
   * @param {number} id 追加服务订单ID
   * @param {number} customerId 客户ID
   * @returns {Promise<any>} 返回支付结果，可能包含订单号(sn)用于微信支付，或直接返回true表示支付完成
   */
  async pay(orderDetailId, id, customerId) {
    const url = additionalService.pay
      .replace('{orderDetailId}', orderDetailId)
      .replace('{id}', id);

    const requestData = { customerId };

    try {
      const res = await request.post(url, requestData);
      console.log('API原始响应:', res);

      const result = analysisRes(res);
      console.log('API解析后结果:', result);

      return result;
    } catch (error) {
      console.error('追加服务支付API调用异常:', error);
      throw error;
    }
  },

  /**
   * 删除追加服务申请
   * @param {number} orderDetailId 订单详情ID
   * @param {number} id 追加服务订单ID
   * @param {number} customerId 客户ID
   * @returns {Promise<any>} 返回删除结果
   */
  async delete(orderDetailId, id, customerId) {
    const res = await request.delete(
      additionalService.delete
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', id),
      { customerId }
    );
    return analysisRes(res);
  },
};
