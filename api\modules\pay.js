import siteinfo from '../../siteinfo';
import request, { analysisRes } from '../request';
import config from '../config';
import tools from '../../common/Tools';
const $tools = new tools();

const { apiUrls } = config;
const { pay } = apiUrls;

// 支付锁定状态管理
const paymentLocks = new Map();

export default {
  // 支付
  async pay(sn) {
    const res = await request.post(pay.jsapi, {
      appid: siteinfo.appid,
      sn,
    });
    return analysisRes(res);
  },

  /** 获取支付签名 */
  async getPaySign({ timeStamp, nonceStr, perpayId }) {
    const res = await request.post(pay.pay_sign, {
      appid: siteinfo.appid,
      timeStamp,
      nonceStr,
      perpayId,
    });
    return analysisRes(res);
  },

  /** 商户订单号查询订单 */
  async getTransactionsBySN(sn) {
    const res = await request.get(pay.getTransactionsBySN.replace(':sn', sn));
    return analysisRes(res);
  },

  /**
   * 检查订单支付状态
   * 注意：此方法有两个调用场景：
   * 1. 支付前预检查：新订单在微信支付系统中不存在，返回false是正常的
   * 2. 支付后验证：支付成功后应该能查到订单且状态为SUCCESS
   * @param {string} sn 商户订单号
   * @param {boolean} isPreCheck 是否为支付前预检查，true时不输出错误日志
   * @returns {boolean} 是否已支付成功
   */
  async checkOrderPaymentStatus(sn, isPreCheck = false) {
    try {
      const orderStatus = await this.getTransactionsBySN(sn);
      const isPaid = orderStatus?.trade_state === 'SUCCESS';

      if (isPreCheck) {
        // 支付前预检查：订单不存在或未支付都是正常的
        console.log(`支付前预检查结果: ${isPaid ? '已支付' : '未支付或订单不存在（正常）'}`);
      } else {
        // 支付后验证：应该能查到订单状态
        console.log(`支付后验证结果: ${isPaid ? '支付成功' : '支付状态异常'}`);
      }

      return isPaid;
    } catch (error) {
      if (isPreCheck) {
        // 支付前预检查：查询失败是正常的（新订单不存在）
        console.log('支付前预检查：订单不存在（正常，继续支付流程）');
        return false;
      } else {
        // 支付后验证：查询失败需要记录错误
        console.error('支付后状态验证失败:', error);
        return false;
      }
    }
  },

  /** 锁定订单支付 */
  lockPayment(sn) {
    if (paymentLocks.has(sn)) {
      return false;
    }
    paymentLocks.set(sn, true);
    return true;
  },

  /** 解锁订单支付 */
  unlockPayment(sn) {
    paymentLocks.delete(sn);
  },

  /**
   * 封装的微信支付方法
   *
   * 支付流程说明：
   * 1. 支付前预检查：查询订单是否已支付（新订单查不到是正常的）
   * 2. 支付锁定：防止重复点击支付按钮
   * 3. 获取支付参数：调用后端获取prepay_id
   * 4. 获取支付签名：准备微信支付所需的签名
   * 5. 唤起微信支付：调用wx.requestPayment
   * 6. 支付后验证：确认微信支付系统中的订单状态
   *
   * @param {Object} params 支付参数
   * @param {string} params.sn 商户订单号
   * @param {Function} params.onOk 支付成功回调
   * @param {Function} params.onCancel 支付取消回调
   * @param {Function} params.onError 支付失败回调
   * @param {Function} params.complete 支付完成回调（无论成功失败都会调用）
   */
  async doPay({ sn, onOk, onCancel, onError, complete }) {
    const _this = this;

    console.log('=== 微信支付流程开始 ===');
    console.log('商户订单号:', sn);

    try {
      // 1. 支付前预检查：防止重复支付
      // 注意：新订单在微信支付系统中不存在是正常的，不会影响后续支付流程
      console.log('1. 支付前预检查：检查订单是否已支付...');
      const isPaid = await this.checkOrderPaymentStatus(sn, true); // true表示这是支付前预检查

      if (isPaid) {
        console.log('订单已支付，无需重复支付');
        wx.showToast({
          title: '订单已支付，无需重复支付',
          icon: 'none',
        });
        if (onOk) onOk();
        return;
      }

      // 2. 支付锁定：防止用户重复点击支付按钮
      console.log('2. 检查支付锁定状态...');
      if (!this.lockPayment(sn)) {
        console.log('订单正在支付中，请勿重复操作');
        wx.showToast({
          title: '正在支付中，请勿重复操作',
          icon: 'none',
        });
        return;
      }
      console.log('支付锁定成功');

      // 3. 获取支付参数：调用后端接口获取微信支付所需的prepay_id
      console.log('3. 调用后端支付接口获取prepay_id...');
      const res = await _this.pay(sn);
      console.log('后端支付接口返回结果:', res);
      wx.hideLoading();

      if (typeof res === 'string') {
        console.log('返回结果为字符串类型:', res);
        if (res === '0') {
          console.log('完成0元订单支付。');
          if (onOk) onOk();
          return;
        }
      }

      if (!res.prepay_id) {
        console.log('未获取到prepay_id，支付失败');
        this.unlockPayment(sn);
        wx.showToast({
          title: '网络好像有问题呢~，请稍后再试！',
          icon: 'none',
        });
        return;
      }

      console.log('获取到prepay_id:', res.prepay_id);

      // 4. 获取支付签名：准备微信支付所需的签名参数
      console.log('4. 准备微信支付参数并获取签名...');
      const timeStamp = `${Math.floor(Date.now() / 1000)}`;
      const nonceStr = $tools.guid();
      const perpayId = `prepay_id=${res.prepay_id}`;
      console.log('微信支付参数:', { timeStamp, nonceStr, perpayId });

      const paySign = await _this.getPaySign({ timeStamp, nonceStr, perpayId });
      console.log('获取到支付签名:', paySign);

      // 5. 唤起微信支付：调用微信小程序支付API
      console.log('5. 唤起微信支付...');
      wx.requestPayment({
        timeStamp,
        nonceStr,
        package: perpayId,
        signType: 'RSA',
        paySign,
        async success(res) {
          console.log('微信支付成功回调：', res);
          // 支付后验证：确认微信支付系统中的订单状态
          // 注意：这时应该能查到订单且状态为SUCCESS
          console.log('支付后验证：确认微信支付系统中的订单状态...');
          const verifyStatus = await _this.checkOrderPaymentStatus(sn, false); // false表示这是支付后验证

          if (verifyStatus) {
            console.log('支付状态验证成功，调用onOk回调');
            if (onOk) onOk();
          } else {
            console.log('支付状态验证失败，调用onError回调');
            if (onError) onError();
          }
        },
        fail(res) {
          console.log('微信支付失败回调：', res);
          if (res.errMsg === 'requestPayment:fail cancel') {
            console.log('用户取消支付');
            if (onCancel) onCancel();
          } else {
            console.log('支付失败');
            if (onError) onError();
          }
        },
        complete() {
          console.log('微信支付完成回调');
          _this.unlockPayment(sn); // 解锁支付
          if (complete) complete();
        },
      });
    } catch (error) {
      console.error('=== payApi.doPay 异常 ===');
      console.error('支付过程发生错误:', error);
      console.error('错误堆栈:', error.stack);
      this.unlockPayment(sn);
      if (onError) onError();
    }
  },

  /** 退款 */
  async refund(sn) {
    const res = await request.post(pay.refund.replace(':sn', sn));
    return analysisRes(res);
  },
};
