import siteinfo from '../../siteinfo';
import request, { analysisRes } from '../request';
import config from '../config';
import tools from '../../common/Tools';
const $tools = new tools();

const { apiUrls } = config;
const { pay } = apiUrls;

// 支付锁定状态管理
const paymentLocks = new Map();

export default {
  // 支付
  async pay(sn) {
    const res = await request.post(pay.jsapi, {
      appid: siteinfo.appid,
      sn,
    });
    return analysisRes(res);
  },

  /** 获取支付签名 */
  async getPaySign({ timeStamp, nonceStr, perpayId }) {
    const res = await request.post(pay.pay_sign, {
      appid: siteinfo.appid,
      timeStamp,
      nonceStr,
      perpayId,
    });
    return analysisRes(res);
  },

  /** 商户订单号查询订单 */
  async getTransactionsBySN(sn) {
    const res = await request.get(pay.getTransactionsBySN.replace(':sn', sn));
    return analysisRes(res);
  },

  /** 检查订单支付状态 */
  async checkOrderPaymentStatus(sn) {
    try {
      const orderStatus = await this.getTransactionsBySN(sn);
      return orderStatus?.trade_state === 'SUCCESS';
    } catch (error) {
      console.error('检查订单支付状态失败:', error);
      return false;
    }
  },

  /** 锁定订单支付 */
  lockPayment(sn) {
    if (paymentLocks.has(sn)) {
      return false;
    }
    paymentLocks.set(sn, true);
    return true;
  },

  /** 解锁订单支付 */
  unlockPayment(sn) {
    paymentLocks.delete(sn);
  },

  /** 封装的支付方法 */
  async doPay({ sn, onOk, onCancel, onError, complete }) {
    const _this = this;

    console.log('=== payApi.doPay 开始 ===');
    console.log('订单号:', sn);

    try {
      // 1. 检查是否已经支付
      console.log('1. 检查订单支付状态...');
      const isPaid = await this.checkOrderPaymentStatus(sn);
      console.log('订单支付状态检查结果:', isPaid);

      if (isPaid) {
        console.log('订单已支付，无需重复支付');
        wx.showToast({
          title: '订单已支付，无需重复支付',
          icon: 'none',
        });
        if (onOk) onOk();
        return;
      }

      // 2. 检查支付锁定状态
      console.log('2. 检查支付锁定状态...');
      if (!this.lockPayment(sn)) {
        console.log('订单正在支付中，请勿重复操作');
        wx.showToast({
          title: '正在支付中，请勿重复操作',
          icon: 'none',
        });
        return;
      }
      console.log('支付锁定成功');

      // 3. 发起支付
      console.log('3. 调用支付接口...');
      const res = await _this.pay(sn);
      console.log('支付接口返回结果:', res);
      wx.hideLoading();

      if (typeof res === 'string') {
        console.log('返回结果为字符串类型:', res);
        if (res === '0') {
          console.log('完成0元订单支付。');
          if (onOk) onOk();
          return;
        }
      }

      if (!res.prepay_id) {
        console.log('未获取到prepay_id，支付失败');
        this.unlockPayment(sn);
        wx.showToast({
          title: '网络好像有问题呢~，请稍后再试！',
          icon: 'none',
        });
        return;
      }

      console.log('获取到prepay_id:', res.prepay_id);

      // 4. 获取支付签名
      console.log('4. 获取支付签名...');
      const timeStamp = `${Math.floor(Date.now() / 1000)}`;
      const nonceStr = $tools.guid();
      const perpayId = `prepay_id=${res.prepay_id}`;
      console.log('支付参数:', { timeStamp, nonceStr, perpayId });

      const paySign = await _this.getPaySign({ timeStamp, nonceStr, perpayId });
      console.log('获取到支付签名:', paySign);

      // 5. 发起支付请求
      console.log('5. 发起微信支付请求...');
      wx.requestPayment({
        timeStamp,
        nonceStr,
        package: perpayId,
        signType: 'RSA',
        paySign,
        async success(res) {
          console.log('微信支付成功回调：', res);
          // 再次确认支付状态
          console.log('再次确认支付状态...');
          const verifyStatus = await _this.checkOrderPaymentStatus(sn);
          console.log('支付状态验证结果:', verifyStatus);

          if (verifyStatus) {
            console.log('支付状态验证成功，调用onOk回调');
            if (onOk) onOk();
          } else {
            console.log('支付状态验证失败，调用onError回调');
            if (onError) onError();
          }
        },
        fail(res) {
          console.log('微信支付失败回调：', res);
          if (res.errMsg === 'requestPayment:fail cancel') {
            console.log('用户取消支付');
            if (onCancel) onCancel();
          } else {
            console.log('支付失败');
            if (onError) onError();
          }
        },
        complete() {
          console.log('微信支付完成回调');
          _this.unlockPayment(sn); // 解锁支付
          if (complete) complete();
        },
      });
    } catch (error) {
      console.error('=== payApi.doPay 异常 ===');
      console.error('支付过程发生错误:', error);
      console.error('错误堆栈:', error.stack);
      this.unlockPayment(sn);
      if (onError) onError();
    }
  },

  /** 退款 */
  async refund(sn) {
    const res = await request.post(pay.refund.replace(':sn', sn));
    return analysisRes(res);
  },
};
